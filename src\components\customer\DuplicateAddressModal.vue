<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import { CustomerMeasureOrderService } from '@/api/customerOrder.ts';
import { customerStep1PublishStore } from '@/stores/customerGlobal.ts';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';

const duplicateAddressModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const router = useRouter();
const step1Data = customerStep1PublishStore();
const isSubmitting = ref(false);

const openDuplicateAddressModal = () => {
  console.log('openDuplicateAddressModal');
  duplicateAddressModalRef.value?.openModal();
};

const stillPublish = async () => {
  // 防止重複提交
  if (isSubmitting.value) return;

  isSubmitting.value = true;
  try {
    // 仍然要發布訂單
    await submitCustomerMeasureData();
    closeDuplicateAddressModal();
  } finally {
    isSubmitting.value = false;
  }
};

const goBackToPublish = () => {
  // 防止重複點擊
  if (isSubmitting.value) return;

  isSubmitting.value = true;
  // 回到發布頁面
  closeDuplicateAddressModal();
  router.push({
    name: 'customermeasurepublish'
  });
};

const closeDuplicateAddressModal = () => {
  duplicateAddressModalRef.value?.closeModal();
};

const submitCustomerMeasureData = async () => {
  // Call 刊登 API
  if (!step1Data.measureData) return;
  const response = await CustomerMeasureOrderService.publishOrder(step1Data.measureData);
  if (response.status === APIStatusCodeEnum.Success) {
    // 成功後立即綁定訂單
    const bindResponse = await CustomerMeasureOrderService.bindOrder({
      orderIds: [response.result.orderId]
    });
    if (bindResponse.status === APIStatusCodeEnum.Success) {
      // 成功後跳轉訂單成立Modal 清除pinia
      customerStep1PublishStore().cleanData();
      await router.push({
        name: 'customermeasurepublishok'
      });
    } else {
      console.error('error');
    }
  } else {
    console.error('error');
  }
};

defineExpose({ openDuplicateAddressModal });
</script>

<template>
  <DefaultModal 
    title="重複地址預約" 
    :click-outside-close="false" 
    :show-close-button="false"
    ref="duplicateAddressModalRef"
    @closeModal="closeDuplicateAddressModal()"
  >
    <div class="flex-col m-3 text-black">
      <p>您之前已經有預約丈量服務訂單</p>
      <p>您確定要使用相同地址重複預約?</p>
      <div class="mt-1 md:mt-6 md:grid md:grid-flow-row-dense md:grid-cols-2 md:gap-3 text-color-secondary">
        <button
          type="button"
          class="button-basic w-full bg-color-selected hover:opacity-80 md:col-start-2"
          :disabled="isSubmitting"
          :class="{ 'opacity-50 cursor-not-allowed': isSubmitting }"
          @click="stillPublish()"
        >
          {{ isSubmitting ? '處理中...' : '是' }}
        </button>
        <button
          type="button"
          class="button-basic mt-1 md:mt-0 w-full ring-1 ring-inset ring-gray-300 hover:bg-gray-50 md:col-start-1"
          :disabled="isSubmitting"
          :class="{ 'opacity-50 cursor-not-allowed': isSubmitting }"
          @click="goBackToPublish()"
        >
          {{ isSubmitting ? '處理中...' : '否' }}
        </button>
      </div>
    </div>
  </DefaultModal>
</template>
