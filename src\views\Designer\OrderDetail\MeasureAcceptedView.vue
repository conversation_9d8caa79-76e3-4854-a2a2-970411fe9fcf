<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { MeasureOrderDetailService, OrderDetailService } from '@/api/designerOrder.ts';
import {
  ImagePdfCadKeyContent,
  MeasureOrderAcceptedDetailItem
} from '@/model/response/orderDetail/orderAcceptedDetailResponse.ts';
import { formatFullDateTimeWithDay } from '@/utils/timeFormat.ts';
import HousePhoto from '@/components/Order/OrderDetail/SubComponets/HousePhoto.vue';
import HouseCheck from '@/components/Order/OrderDetail/SubComponets/HouseCheck.vue';
import FloorPlan from '@/components/Order/OrderDetail/SubComponets/FloorPlan.vue';
import HouseNote from '@/components/Order/OrderDetail/SubComponets/HouseNote.vue';
import TextContent from '@/components/Order/OrderDetail/SubComponets/TextContent.vue';
import { useRoute, useRouter } from 'vue-router';
import { contactItemEnum } from '@/model/enum/contactItemEnum.ts';
import {
  ChatBubbleLeftEllipsisIcon,
  ClipboardDocumentListIcon,
  PhoneIcon,
  DevicePhoneMobileIcon
} from '@heroicons/vue/24/outline';
import HezDivider from '@/components/General/HezDivider.vue';
import AppUsePhoneModal from '@/components/Modal/AppUsePhoneModal.vue';
import PhoneNumberModal from '@/components/Modal/PhoneNumberModal.vue';
import AndroidNotSupportSharedWorkerModal from '@/components/Modal/AndroidNotSupportSharedWorkerModal.vue';
import { UserTypeEnum } from '@/model/enum/userTypeEnum.ts';
import { useChatHubStore } from '@/stores/global.ts';
import ChatRoom from '@/views/ChatRoom.vue';
import NoteBook from '@/components/ChatRoom/NoteBook.vue';
import { parseJsonString } from '@/utils/JsonStringFormat.ts';
import { MeasureOrderStatusEnum } from '@/model/enum/orderStatus.ts';
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';
import { toastInfo } from '@/utils/toastification.ts';

const route = useRoute();
const router = useRouter();
const orderId = route.params.id as string;
const chatHubStore = useChatHubStore();
const chatRoomRef = ref<InstanceType<typeof ChatRoom>>();
const noteBookRef = ref<InstanceType<typeof NoteBook>>();
const deleteModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const finishModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const appUsePhoneModalRef = ref<InstanceType<typeof AppUsePhoneModal>>();
const sharedWorkerNotSupportModalRef = ref<InstanceType<typeof AndroidNotSupportSharedWorkerModal> | null>(null);
const isChatRoomShow = ref(true);
const phoneNumberModalRef = ref<InstanceType<typeof PhoneNumberModal> | null>(null);

const measureDetail = ref<MeasureOrderAcceptedDetailItem>({
  status: MeasureOrderStatusEnum.SurveyDone,
  designerId: '',
  orderId: '',
  customerName: '',
  address: {
    fullName: '',
    simpleName: '',
    location: {
      lat: 0,
      lng: 0
    }
  },
  measureTime: '',
  customerId: '',
  customerAvatarUrl: '',
  customerPhone: '',
  content: {
    houseInfo: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    photos: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    houseCheck: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    floorPlan: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    note: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    constructionRequest: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    waterQuality: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    airQuality: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    noise: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    humidity: {
      updateTime: '',
      updateCount: 0,
      content: []
    },
    radiation: {
      updateTime: '',
      updateCount: 0,
      content: []
    }
  },
  chatRoomId: '',
  createTime: '',
  refreshTime: '',
  isDeleted: false
});

const getMeasureOrderData = async () => {
  try {
    const detailData = (await OrderDetailService.getMeasureAccepted({ orderId: orderId })).result;
    if (detailData) {
      measureDetail.value = {
        status: detailData.status,
        designerId: detailData.designerId,
        orderId: detailData.orderId,
        customerName: detailData.customerName,
        address: detailData.address,
        measureTime: formatFullDateTimeWithDay(detailData.measureTime),
        customerId: detailData.customerId,
        customerAvatarUrl: detailData.customerAvatarUrl,
        customerPhone: detailData.customerPhone,
        content: detailData.content,
        chatRoomId: detailData.chatRoomId,
        createTime: detailData.createTime,
        refreshTime: detailData.refreshTime,
        isDeleted: detailData.isDeleted
      };
      // console.log('measureDetail', measureDetail.value);
    }
  } catch (error) {
    console.log(error);
  }
};

const contactItemClicked = (type: contactItemEnum) => {
  switch (type) {
    case contactItemEnum.Phone:
      appUsePhoneModalRef.value?.openModal();
      break;
    case contactItemEnum.Meet:
      phoneNumberModalRef.value?.openModal();
      break;
    case contactItemEnum.Chat:
      if (chatHubStore._worker === null) {
        sharedWorkerNotSupportModalRef.value?.openModal();
      } else {
        chatRoomRef.value?.showChatRoom();
      }
      break;
    case contactItemEnum.Note:
      noteBookRef.value?.openModal();
  }
};

const openFinishModal = () => {
  finishModalRef.value?.openModal();
};

const openDeleteOrderModal = () => {
  if (measureDetail.value.status === MeasureOrderStatusEnum.SurveyorComing) {
    deleteModalRef.value?.openModal();
  }
};

const deleteOrder = async () => {
  const res = await MeasureOrderDetailService.DeleteOrder({ orderId: orderId });
  if (res.status === APIStatusCodeEnum.Success) {
    toastInfo('訂單已刪除');
    //回訂單紀錄
    await router.push({
      name: 'designerOrderList'
    });
  }
};

const finishOrder = async () => {
  const res = await MeasureOrderDetailService.FinishOrder({ orderId: orderId });
  if (res.status === APIStatusCodeEnum.Success) {
    toastInfo('訂單完成');
    await getMeasureOrderData();
    finishModalRef.value?.closeModal();
  }
};

const canFinishOrder = computed(() => {
  const mapJsonStringHouseInfo = measureDetail.value.content.houseInfo.content.map(parseJsonString);
  const checkFloorPlanPass = (floorPlan: ImagePdfCadKeyContent) => {
    const imagePass = floorPlan.content.every((item) => item.images.length > 0);
    const pdfPass = floorPlan.content.every((item) => item.pdf.url !== '');
    const cadPass = floorPlan.content.every((item) => item.cad.url !== '');
    return !(!imagePass || !pdfPass || !cadPass);
  };

  const houseInfoPass = mapJsonStringHouseInfo.every((item) => item.text.content.text !== '');
  const housePhotoPass = measureDetail.value.content.photos.content.every((item) => item.media.length > 0);
  const houseCheckPass = measureDetail.value.content.houseCheck.content.every((item) => item.media.length > 0);
  const floorPlanPass = checkFloorPlanPass(measureDetail.value.content.floorPlan);
  return houseInfoPass && housePhotoPass && houseCheckPass && floorPlanPass;
});

const changeChatRoomShow = (isShow: boolean) => {
  isChatRoomShow.value = isShow;
};

onMounted(async () => {
  await getMeasureOrderData();
});
</script>

<template>
  <div class="my-8 flex w-full flex-col gap-y-8">
    <div class="cus-border text-lg">
      <h2 class="text-center text-2xl font-bold">空間丈量</h2>
      <div class="flex flex-col gap-y-1 p-4" @click="openDeleteOrderModal()">
        <div class="flex gap-x-2">
          <p class="text-nowrap font-bold">支付方式</p>
          <p class="font-bold">到府收取現金</p>
        </div>
        <div class="flex gap-x-2">
          <p class="text-nowrap font-bold">總金額</p>
          <p class="font-bold">NT$ 2,000</p>
        </div>
        <div class="flex gap-x-2">
          <p class="text-nowrap font-bold">屋主姓名</p>
          <p class="font-bold">{{ measureDetail.customerName }}</p>
        </div>
        <div class="flex gap-x-2">
          <p class="text-nowrap font-bold">丈量地址</p>
          <p class="font-bold">{{ measureDetail.address.fullName }}</p>
        </div>
        <div class="flex gap-x-2">
          <p class="text-nowrap font-bold">預約時間</p>
          <p class="font-bold">{{ measureDetail.measureTime }}</p>
        </div>
      </div>
      <HezDivider />
      <div class="flex justify-around">
        <button
          class="hover-zoom flex flex-col items-center text-center"
          @click="contactItemClicked(contactItemEnum.Phone)"
        >
          <PhoneIcon class="mb-2 h-8 w-8 md:h-16 md:w-16" />
          <span class="font-bold max-md:text-base">免費語音</span>
        </button>
        <button
          class="hover-zoom flex flex-col items-center text-center"
          @click="contactItemClicked(contactItemEnum.Meet)"
        >
          <DevicePhoneMobileIcon class="mb-2 h-8 w-8 md:h-16 md:w-16" />
          <span class="font-bold max-md:text-base">手機電話</span>
        </button>
        <button
          class="hover-zoom flex flex-col items-center text-center"
          @click="contactItemClicked(contactItemEnum.Chat)"
        >
          <ChatBubbleLeftEllipsisIcon class="mb-2 h-8 w-8 md:h-16 md:w-16" />
          <span class="font-bold max-md:text-base">聊天室</span>
        </button>
        <button
          class="hover-zoom flex flex-col items-center text-center"
          @click="contactItemClicked(contactItemEnum.Note)"
        >
          <ClipboardDocumentListIcon class="mb-2 h-8 w-8 md:h-16 md:w-16" />
          <span class="font-bold max-md:text-base">紀錄本</span>
        </button>
      </div>
      <template v-if="measureDetail.status < MeasureOrderStatusEnum.SurveyDone">
        <HezDivider />
        <!-- 按鈕 -->
        <div class="flex justify-evenly gap-2 max-md:flex-col">
          <button
            :class="{
              'w-full rounded-lg p-4 text-xl font-bold text-black shadow-md': true,
              'cursor-not-allowed bg-gray-300 hover:bg-gray-300': !canFinishOrder,
              'flashing-light': canFinishOrder
            }"
            :disabled="!canFinishOrder"
            @click="canFinishOrder && openFinishModal()"
          >
            完成訂單
          </button>
        </div>
      </template>
    </div>
    <TextContent
      v-model="measureDetail.content.houseInfo"
      :orderId="orderId"
      title="房屋資訊"
      contentName="houseInfo"
      :canNotDeleteCount="4"
      :disabled="false"
      :canAddUnit="true"
    />
    <HousePhoto v-model="measureDetail.content.photos" :orderId="orderId" :disabled="false" />
    <HouseCheck v-model="measureDetail.content.houseCheck" :orderId="orderId" :disabled="false" />
    <FloorPlan v-model="measureDetail.content.floorPlan" :orderId="orderId" :disabled="false" />
    <TextContent
      v-model="measureDetail.content.constructionRequest"
      :orderId="orderId"
      title="裝潢需求"
      contentName="constructionRequest"
      :canNotDeleteCount="16"
      :disabled="false"
      :canAddUnit="false"
    />
    <TextContent
      v-model="measureDetail.content.waterQuality"
      :orderId="orderId"
      title="水質檢測"
      contentName="waterQuality"
      :canNotDeleteCount="0"
      :disabled="false"
      :canAddUnit="true"
    />
    <TextContent
      v-model="measureDetail.content.airQuality"
      :orderId="orderId"
      title="空氣檢測"
      contentName="airQuality"
      :canNotDeleteCount="0"
      :disabled="false"
      :canAddUnit="true"
    />
    <TextContent
      v-model="measureDetail.content.noise"
      :orderId="orderId"
      title="噪音檢測"
      subTitle="(安靜:50dB，吵雜:60dB)"
      contentName="noise"
      :canNotDeleteCount="1"
      :disabled="false"
      :canAddUnit="true"
    />
    <TextContent
      v-model="measureDetail.content.humidity"
      :orderId="orderId"
      title="濕度檢測"
      subTitle="(正常:40~60%，潮濕:70%)"
      contentName="humidity"
      :canNotDeleteCount="1"
      :disabled="false"
      :canAddUnit="true"
    />
    <TextContent
      v-model="measureDetail.content.radiation"
      :orderId="orderId"
      title="電磁輻射"
      contentName="radiation"
      :canNotDeleteCount="0"
      :disabled="false"
      :canAddUnit="true"
    />
    <HouseNote v-model="measureDetail.content.note" :orderId="orderId" :disabled="false" />
  </div>

  <div
    v-if="measureDetail.chatRoomId && chatHubStore._worker !== null"
    class="fixed bottom-0 right-0 z-40 flex w-80 flex-col justify-end"
    :class="isChatRoomShow ? 'h-3/5' : 'h-auto'"
  >
    <ChatRoom
      :is-for-meet="false"
      :room-id="measureDetail.chatRoomId"
      :is-designer="true"
      @show-chat-room="changeChatRoomShow"
      ref="chatRoomRef"
    />
  </div>
  <NoteBook :room-id="measureDetail.chatRoomId" :is-designer="true" ref="noteBookRef" />
  <AppUsePhoneModal :user-type="UserTypeEnum.Designer" ref="appUsePhoneModalRef" />
  <AndroidNotSupportSharedWorkerModal :is-designer="true" ref="sharedWorkerNotSupportModalRef" />
  <PhoneNumberModal
    :user-type="UserTypeEnum.Designer"
    :phone-number="measureDetail.customerPhone"
    ref="phoneNumberModalRef"
  />
  <DefaultModal
    title="取消訂單"
    :show-close-button="true"
    :click-outside-close="false"
    modal-width="max-w-md"
    ref="deleteModalRef"
    @closeModal="deleteModalRef?.closeModal()"
  >
    <div class="flex flex-col items-center text-black">
      <div class="flex flex-col items-start p-2">
        <p class="font-bold md:text-lg">請確認是否要取消此訂單?</p>
        <p class="font-bold md:text-lg">若超過三次會被封鎖帳號!</p>
        <button
          type="button"
          class="button-basic mt-2 w-full bg-red-300 ring-1 ring-inset ring-gray-300 hover:bg-red-400"
          @click="deleteOrder()"
        >
          確認取消訂單
        </button>
      </div>
    </div>
  </DefaultModal>

  <DefaultModal
    title="完成訂單"
    :show-close-button="true"
    :click-outside-close="false"
    modal-width="max-w-md"
    ref="finishModalRef"
    @closeModal="finishModalRef?.closeModal()"
  >
    <div class="flex flex-col items-center text-black">
      <div class="flex flex-col items-start p-2">
        <p class="font-bold md:text-lg">請確認已經成功收取全部現金，</p>
        <p class="font-bold md:text-lg">並且所有資料也已完整上傳。</p>
        <button
          type="button"
          class="button-basic mt-2 w-full bg-gray-200 ring-1 ring-inset ring-gray-300 hover:bg-gray-300"
          @click="finishOrder()"
        >
          確認完成訂單
        </button>
      </div>
    </div>
  </DefaultModal>
</template>

<style scoped></style>
