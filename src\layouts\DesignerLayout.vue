<script setup lang="ts">
import { Disclosure, DisclosureButton, DisclosurePanel } from '@headlessui/vue';
import { Bars3Icon, XMarkIcon } from '@heroicons/vue/24/outline';
import { useRoute, useRouter } from 'vue-router';
import { computed, ref } from 'vue';
import { toastInfo } from '@/utils/toastification.ts';
import { useDesignerInfoStore, useDesignerRedirectStore } from '@/stores/designerGlobal.ts';
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';

const initNavigation = [
  { title: '作品主頁', routeName: 'designerportfolio', current: false },
  { title: '訂單列表', routeName: 'designerOrderList', current: false },
  { title: '個人資訊', routeName: 'designerprofile', current: false },
  { title: '設計師App', routeName: 'designerappdownload', current: false },
  { title: '前往客戶端', routeName: 'customerhome', current: false }
];
const route = useRoute();
const router = useRouter();
const designerInfoStore = useDesignerInfoStore();
const redirectLink = useDesignerRedirectStore();

const updatedNavigation = computed(() =>
  initNavigation.map((item) => {
    item.current = route.name === item.routeName;
    return item;
  })
);

const switchModal = ref<InstanceType<typeof DefaultModal> | null>(null);

const handleNavigationClick = async (item: { title: string; routeName: string; current: boolean }) => {
  if (!designerInfoStore.loginState) {
    if (item.title === '作品主頁' || item.title === '訂單列表' || item.title === '個人資訊') {
      redirectLink.setRedirectRoute(item.routeName);
      toastInfo('請先註冊或登入');
      await router.push({ name: 'designerregister' });
      return;
    }
  }

  if (item.title === '作品主頁' && designerInfoStore.loginState) {
    await router.push({ name: 'designerportfolio', params: { designerId: designerInfoStore.userId } });
    return;
  }

  if (item.routeName === 'customerhome') {
    // 彈出對話框說明即將切換
    switchModal.value?.openModal();
    return;
  }
  await router.push({ name: item.routeName });
};
</script>

<template>
  <div class="text-ch flex min-h-full flex-col">
    <Disclosure
      as="nav"
      class="sticky top-0 z-50 shadow-gray-300 max-md:border-b-2 md:shadow-md"
      style="background-color: #f5f4f2"
      v-slot="{ open }"
    >
      <div class="mx-auto max-w-7xl px-4">
        <div class="flex justify-between">
          <div class="flex items-center gap-x-2">
            <div class="flex flex-shrink-0 items-center py-2">
              <router-link to="/designer/home">
                <img class="w-60 object-contain max-md:max-w-48" src="/image/web_logo_Designer.png" alt="" />
              </router-link>
            </div>
            <div class="hidden lg:-my-px lg:ml-6 lg:flex lg:space-x-8">
              <router-link
                v-for="item in updatedNavigation"
                :key="item.title"
                :to="{ name: item.routeName }"
                @click.prevent="handleNavigationClick(item)"
                :class="[
                  item.current ? 'border-secondary font-bold' : 'hover:border-secondary border-transparent font-medium',
                  'text-color-primary inline-flex cursor-pointer items-center border-b-2 px-3 py-2 text-base'
                ]"
                :aria-current="item.current ? 'page' : undefined"
              >
                {{ item.title }}
              </router-link>
            </div>
          </div>
          <div class="-mr-2 flex lg:hidden">
            <!-- 行動端 -->
            <DisclosureButton class="inline-flex items-center justify-center p-2 text-black">
              <span class="sr-only">Open main menu</span>
              <Bars3Icon v-if="!open" class="block h-6 w-6" aria-hidden="true" />
              <XMarkIcon v-else class="block h-6 w-6" aria-hidden="true" />
            </DisclosureButton>
          </div>
        </div>
      </div>

      <DisclosurePanel class="lg:hidden">
        <!-- 行動端 -->
        <div class="space-y-1 px-2 pb-3 pt-2 sm:px-3">
          <DisclosureButton
            v-for="item in updatedNavigation"
            :key="item.title"
            as="a"
            @click="handleNavigationClick(item)"
            :class="[
              item.current ? 'bg-color-selected' : 'hover:bg-color-selected hover:bg-opacity-75',
              'text-color-secondary block rounded-md px-3 py-2 text-base font-medium'
            ]"
            :aria-current="item.current ? 'page' : undefined"
            >{{ item.title }}
          </DisclosureButton>
        </div>
      </DisclosurePanel>
    </Disclosure>

    <main class="flex-grow">
      <div class="max-w-full">
        <div class="mx-auto flex justify-center">
          <div class="flex w-full flex-col items-center max-xl:mx-8 xl:max-w-[1248px]">
            <slot />
          </div>
        </div>
      </div>
    </main>
    <footer>
      <div
        class="flex max-w-full items-center justify-center gap-8 bg-gray-100 px-4 py-4 max-md:flex-col max-md:gap-4 sm:px-6 lg:px-8"
      >
        <div class="flex flex-col items-center gap-y-2">
          <p class="text-center text-sm">© 2024 Home Easy. Created by MMSLab.</p>
          <div class="flex justify-center gap-2 max-md:flex-col">
            <p class="text-center text-sm">
              <span class="font-bold">聯絡Email：</span>
              <EMAIL>
            </p>
            <p class="text-center text-sm">
              <span class="font-bold">聯絡電話：</span>
              (02)2771-2171分機2232
            </p>
          </div>
        </div>
        <div class="flex flex-row items-center gap-2 md:flex-col">
          <a href="https://play.google.com/store/apps/details?id=lab.homeeasy.designer" target="_blank">
            <img src="/vectors/general/AndroidDownload.svg" alt="Android download" class="hover-zoom w-24" />
          </a>
          <a href="https://apps.apple.com/us/app/homeeasy-designer/id6477273359" target="_blank">
            <img src="/vectors/general/IOSDownload.svg" alt="IOS download" class="hover-zoom w-24" />
          </a>
        </div>
      </div>
    </footer>
  </div>

  <DefaultModal
    title="切換至客戶端"
    :click-outside-close="true"
    :show-close-button="true"
    @closeModal="switchModal?.closeModal()"
    modalWidth="max-w-md"
    ref="switchModal"
  >
    <div class="m-4 flex flex-col items-center space-y-4 text-black">
      <p>您目前在設計師端</p>
      <p>請問您要切換至客戶端嗎?</p>
      <button type="button" class="cus-btn w-full p-2" @click="router.push({ name: 'customerhome' })">確定切換</button>
    </div>
  </DefaultModal>
</template>
