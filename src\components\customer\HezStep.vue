<script setup lang="ts">
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import { ref } from 'vue';

const step1Ref = ref<InstanceType<typeof DefaultModal> | null>(null);
const step2Ref = ref<InstanceType<typeof DefaultModal> | null>(null);
const step3Ref = ref<InstanceType<typeof DefaultModal> | null>(null);
const step4Ref = ref<InstanceType<typeof DefaultModal> | null>(null);
</script>

<template>
  <div class="w-full py-8">
    <h2 class="mb-8 text-center text-2xl font-bold">家易量化服務流程</h2>
    <div class="flex flex-col items-center justify-between space-y-4 md:flex-row md:space-x-4 md:space-y-0">
      <div
        class="flex flex-col items-center space-y-4 max-md:w-full md:flex-row md:space-x-4 md:space-y-0"
        @click="step1Ref?.openModal"
      >
        <div
          class="hover-zoom flex cursor-pointer flex-col items-center rounded-xl bg-white text-black shadow-md shadow-gray-400 max-md:w-full"
        >
          <img src="/vectors/customerStep/step1.svg" alt="Step1" class="mx-4 my-8 h-60 w-56 md:h-40 md:w-40" />
          <div class="cus-hezStep-color w-full rounded-b-xl py-8 text-center text-gray-600 max-md:space-y-3 md:py-1.5">
            <p class="text-xl font-bold md:text-lg">Step1</p>
            <p class="text-xl font-bold md:text-lg">空間丈量</p>
          </div>
        </div>
      </div>
      <svg
        class="h-12 w-12 max-md:rotate-90 md:h-16 md:w-16"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7"></path>
      </svg>
      <div
        class="flex flex-col items-center space-y-4 max-md:w-full md:flex-row md:space-x-4 md:space-y-0"
        @click="step2Ref?.openModal"
      >
        <div
          class="hover-zoom flex cursor-pointer flex-col items-center rounded-xl bg-white text-black shadow-md shadow-gray-400 max-md:w-full"
        >
          <img src="/vectors/customerStep/step2.svg" alt="Step2" class="mx-4 my-8 h-60 w-56 md:h-40 md:w-40" />
          <div class="cus-hezStep-color w-full rounded-b-xl py-8 text-center text-gray-600 max-md:space-y-3 md:py-1.5">
            <p class="text-xl font-bold md:text-lg">Step2</p>
            <p class="text-xl font-bold md:text-lg">室內設計</p>
          </div>
        </div>
      </div>
      <svg
        class="h-12 w-12 max-md:rotate-90 md:h-16 md:w-16"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7"></path>
      </svg>
      <div
        class="flex flex-col items-center space-y-4 max-md:w-full md:flex-row md:space-x-4 md:space-y-0"
        @click="step3Ref?.openModal"
      >
        <div
          class="hover-zoom flex cursor-pointer flex-col items-center rounded-xl bg-white text-black shadow-md shadow-gray-400 max-md:w-full"
        >
          <img src="/vectors/customerStep/step3.svg" alt="Step3" class="mx-4 my-8 h-60 w-56 md:h-40 md:w-40" />
          <div class="cus-hezStep-color w-full rounded-b-xl py-8 text-center text-gray-600 max-md:space-y-3 md:py-1.5">
            <p class="text-xl font-bold md:text-lg">Step3</p>
            <p class="text-xl font-bold md:text-lg">裝潢施工</p>
          </div>
        </div>
      </div>
      <svg
        class="h-12 w-12 max-md:rotate-90 md:h-16 md:w-16"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7"></path>
      </svg>
      <div
        class="flex flex-col items-center space-y-4 max-md:w-full md:flex-row md:space-x-4 md:space-y-0"
        @click="step4Ref?.openModal"
      >
        <div
          class="hover-zoom flex cursor-pointer flex-col items-center rounded-xl bg-white text-black shadow-md shadow-gray-400 max-md:w-full"
        >
          <img src="/vectors/customerStep/step4.svg" alt="Step4" class="mx-4 my-8 h-60 w-56 md:h-40 md:w-40" />
          <div class="cus-hezStep-color w-full rounded-b-xl py-8 text-center text-gray-600 max-md:space-y-3 md:py-1.5">
            <p class="text-xl font-bold md:text-lg">Step4</p>
            <p class="text-xl font-bold md:text-lg">發票節稅</p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <DefaultModal
    title="空間丈量服務內容"
    :show-close-button="true"
    :click-outside-close="true"
    ref="step1Ref"
    @closeModal="step1Ref?.closeModal"
  >
    <div class="container mx-auto p-4 text-left">
      <p class="mb-4">
        家易 (Home Easy)
        提供專業的「到府丈量服務」，方便您進行後續「室內設計」和「裝潢施工」。透過我們的服務，您可以獲得以下兩項專業服務：
      </p>
      <ol class="list-inside list-decimal space-y-4">
        <p class="text-lg font-bold">1) 室內平面圖</p>
        <p>
          我們將進行室內空間、室內高度、窗戶、梁柱、連續壁位置、實際坪數等詳細的丈量，並提供您 jpg、pdf、cad
          等檔案格式的專業平面圖。
        </p>
        <p class="text-lg font-bold">2) 房屋健檢</p>
        <p>
          我們提供的非專業技師的房屋檢測服務，由您主動告知是否有漏水、壁癌、積水、傾斜等相關問題，家易的丈量師會到現場紀錄、拍照，使用相關檢測儀器再次確認，方便將來在裝潢施工時進行處理，家易並不提供任何維修和保證服務。
        </p>
        <p class="text-red-600">
          現在，您只需要至 APP 商店評價並留言，即可享受
          <span class="font-bold">優惠特價 2,000 元。</span>
        </p>
        <p class="text-blue-600">原價為 20,000 元的丈量服務，現在只需要支付 2,000 元即可享受。</p>
      </ol>
      <p class="mt-4">此外，如果您在後續進行室內設計，丈量服務費用也可以用於抵扣室內設計費用，讓您的預算更有彈性。</p>
    </div>
  </DefaultModal>
  <DefaultModal
    title="室內設計服務內容"
    :show-close-button="true"
    :click-outside-close="true"
    ref="step2Ref"
    @closeModal="step2Ref?.closeModal"
  >
    <div class="container mx-auto p-4 text-left">
      <p class="mb-4">
        透過家易 (Home Easy)
        提供專業的「即時報價服務」，您可以獲取不同優質設計師提供的報價及預估完成時間。通常室內設計費用為
        <span class="font-bold text-blue-600">每坪約 3,000 元至 12,000 元</span>，
        實際費用則以設計師報價為主，大坪數有折扣。如果，您將來選擇同一位設計師進行裝潢施工，則室內設計費用可抵扣未來的裝潢施工費用。
      </p>
      <ol class="list-inside list-decimal space-y-4">
        <p class="text-lg font-bold">1) 設計師選擇與評價系統</p>
        <p>
          您可以根據設計師的過去作品、風格、專業知識和評價選擇合適的室內設計師。我們還提供設計師的詳細介紹和用戶評價，讓您能全方位了解每位設計師的專長和特色。
        </p>
        <p class="text-lg font-bold">2) 室內設計師媒合服務</p>
        <p>
          一旦媒合成功，設計師將為您提供全套的設計服務，包括「平面配置圖」、「水電設計圖」、「天花板圖」、「各空間立面圖」，以及「完工後的
          3D 預覽」。
        </p>
        <p class="text-lg font-bold">3) 大數據自動化估價</p>
        <p>
          利用大數據分析，準確預估裝潢費用範圍，並提供可靠的預算參考。使您能夠更好地掌握預期花費，避免超出預算的困擾。
        </p>
      </ol>
    </div>
  </DefaultModal>
  <DefaultModal
    title="裝潢施工服務內容"
    :show-close-button="true"
    :click-outside-close="true"
    ref="step3Ref"
    @closeModal="step3Ref?.closeModal"
  >
    <div class="container mx-auto p-4 text-left">
      <p class="mb-4">
        我們系統具有兩大優勢，結合了首創的大數據估價服務和專業的分期驗收服務，確保您獲得最佳的服務品質。
      </p>
      <ol class="list-inside list-decimal space-y-4">
        <p class="text-lg font-bold">1) 專業的分期驗收服務</p>
        <p>確保裝潢按預期進行，避免設計與實際落差或施工品質不佳的問題，享受高品質的裝潢設計和施工服務。</p>
        <p class="text-lg font-bold">2) 裝潢施工抵免優惠</p>
        <p>如果從我們的室內設計師中選擇同一位設計師進行裝潢施工，我將會幫您將室內設計費用抵免裝潢施工費用。</p>
        <p class="text-lg font-bold">3) 施工團隊的報價與比價系統</p>
        <p>
          您可以以大數據施工團隊的報價和用戶評價，讓您能全方位了解每個施工團隊的專長和特色，經過比價後，選擇適合您的施工團隊。
        </p>
      </ol>
    </div>
  </DefaultModal>
  <DefaultModal
    title="發票節稅服務內容"
    :show-close-button="true"
    :click-outside-close="true"
    ref="step4Ref"
    @closeModal="step4Ref?.closeModal"
  >
    <div class="container mx-auto p-4 text-left">
      <p class="mb-4">家易會在每次的服務後都會開立發票，並提供專業的會計諮詢，提供您專業的節稅與裝潢補助諮詢。</p>
      <p class="">家易會列出每次服務的發票明細，包含服務日期、服務金額、設計師名稱、服務內容等資訊供您查看。</p>
    </div>
  </DefaultModal>
</template>
