<script setup lang="ts">
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import 'vue-advanced-cropper/dist/style.css';
import { Cropper } from 'vue-advanced-cropper';
import { ref } from 'vue';

const props = defineProps<{
  cropperImageTitle: string;
  cropperAspectRatio: number;
}>();

const cropperAttribute = ref<{ cropperImage: string | null; cropperFileName: string | null }>({
  cropperImage: null,
  cropperFileName: null
});

const modalRef = ref<InstanceType<typeof DefaultModal> | null>(null);
const cropperRef = ref<InstanceType<typeof Cropper> | null>(null);
let resolveFn: ((file: File) => void) | null = null;
let rejectFn: (() => void) | null = null;

// 縮小圖片
const resizeImage = (file: File, maxWidth = 800, maxHeight = 600): Promise<string> => {
  return new Promise((resolve) => {
    const img = new Image();
    // 建立 canvas 來繪製縮小後的圖片
    const canvas = document.createElement('canvas');
    // 取得 2D 繪圖內容（! 表示確定不是 null）
    const ctx = canvas.getContext('2d')!;

    img.onload = () => {
      // 計算縮小比例（取最小值確保不超出限制，且不放大）
      const ratio = Math.min(maxWidth / img.width, maxHeight / img.height, 1);
      canvas.width = img.width * ratio;
      canvas.height = img.height * ratio;
      // 在 canvas 上繪製縮小後的圖片
      ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
      resolve(canvas.toDataURL('image/png'));
    };
    // 將檔案轉換為 URL 並載入圖片
    img.src = URL.createObjectURL(file);
  });
};

// 打開裁切圖片Modal
const openCropImageModal = async (file: File): Promise<File> => {
  console.log('openCropImageModal', file);
  cropperAttribute.value.cropperImage = await resizeImage(file, 448, 448);
  cropperAttribute.value.cropperFileName = file.name;
  modalRef.value?.openModal();

  return new Promise<File>((resolve, reject) => {
    resolveFn = resolve;
    rejectFn = reject;
  });
};

// 確認裁切
const handleConfirm = async () => {
  if (!cropperRef.value) return;
  // 取得裁切完的圖片
  const { canvas } = cropperRef.value.getResult();
  if (!canvas || !cropperAttribute.value.cropperFileName) return;
  // 將 canvas 轉換為 Blob 二進位圖片格式
  const blob = await new Promise<Blob | null>((resolve) => canvas.toBlob((b) => resolve(b)));

  if (blob) {
    const croppedFile = new File([blob], cropperAttribute.value.cropperFileName, {
      type: blob.type
    });
    resolveFn?.(croppedFile);
    cleanup();
    modalRef.value?.closeModal();
  }
};

// 取消裁切
const handleCancel = () => {
  rejectFn?.();
  cleanup();
  console.log('CropImageModal');
  modalRef.value?.closeModal();
};

// 清除狀態
const cleanup = () => {
  cropperAttribute.value.cropperImage = null;
  cropperAttribute.value.cropperFileName = null;
  resolveFn = null;
  rejectFn = null;
};

defineExpose({ openCropImageModal });
</script>

<template>
  <DefaultModal
    :title="props.cropperImageTitle"
    :show-close-button="true"
    :click-outside-close="false"
    ref="modalRef"
    @closeModal="handleCancel()"
  >
    <div class="flex-center mx-auto">
      <Cropper
        ref="cropperRef"
        :src="cropperAttribute.cropperImage"
        :resize-image="false"
        :image-restriction="'fill-area'"
        :stencil-props="{
          aspectRatio: props.cropperAspectRatio
        }"
      />
    </div>
    <button class="flex-center cus-btn mx-auto mt-4 w-[95%] px-4 py-4" @click="handleConfirm">確認裁切</button>
  </DefaultModal>
</template>
