<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
const route = useRoute();
const routeName = ref<string>(route.name as string);

onMounted(() => {
  const userAgent = navigator.userAgent.toLowerCase();

  if (routeName.value.includes('customer')) {
    if (/iphone|ipad|ipod|android/.test(userAgent)) {
      window.location.href = 'https://www.homeeasy.app/customer/appdownload';
    } else {
      window.location.href = 'https://www.homeeasy.app/customer';
    }
  } else {
    if (/iphone|ipad|ipod|android/.test(userAgent)) {
      window.location.href = 'https://www.homeeasy.app/designer/appdownload';
    } else {
      window.location.href = 'https://www.homeeasy.app/designer/home';
    }
  }
});
</script>

<template>
  <div class="container mx-auto p-4">跳轉中...</div>
</template>
