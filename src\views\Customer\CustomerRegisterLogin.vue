<script setup lang="ts">
import { onBeforeUnmount, ref, onMounted } from 'vue';
import { SecurityService } from '@/api/security.ts';
import type { RegisterData } from '@/model/general/security.ts';
import { PhoneVerifyStatusEnum } from '@/model/enum/verifyStatusEnum.ts';
import { APIStatusCodeEnum } from '@/model/enum/APIStatusCode.ts';
import { phoneNumberValid } from '@/utils/phoneNumberValid.ts';
import { toastError, toastSuccess } from '@/utils/toastification.ts';
import { passwordValid } from '@/utils/passwordValid.ts';
import { hashPassword } from '@/utils/crypto.ts';
import { EyeIcon, EyeSlashIcon } from '@heroicons/vue/24/outline';
import type { LoginData } from '@/model/general/security.ts';
import { decryptAES } from '@/utils/crypto.ts';
import { CustomerMeasureOrderDetailService, CustomerMeasureOrderService } from '@/api/customerOrder.ts';
import { useRouter } from 'vue-router';
import { useCustomerInfoStore, customerStep1PublishStore } from '@/stores/customerGlobal.ts';
import type { LoginConfig } from '@/model/general/security.ts';

const props = withDefaults(defineProps<LoginConfig>(), {
  mode: 'full',
  redirectRoute: 'customerhome'
});

const phone = ref<string>('');
const verifyCode = ref<string>('');
const phoneVerifyStatus = ref<PhoneVerifyStatusEnum>(PhoneVerifyStatusEnum.Unverified);
const registerInput = ref<RegisterData>({
  phoneVerifyId: '',
  password: '',
  rePassword: '',
  agreeTerm: false
});
const sentCodeCountDown = ref<number | null>(null);
const countdownInterval = ref<number | null>(null);
const showPassword = ref(false);
const showRePassword = ref(false);
const isNowDoingLogin = ref(false);

const loginInput = ref<LoginData>({
  phone: '',
  password: '',
  rememberForShow: true
});
const showLoginPassword = ref(false);
const router = useRouter();
const customerInfoStore = useCustomerInfoStore();
const step1Data = customerStep1PublishStore();

const sendCode = async (phoneNumber: string) => {
  const validResult = phoneNumberValid(phoneNumber);
  if (!validResult) {
    toastError('請輸入正確的手機號碼');
    return;
  } else {
    const sendVerifyCodeResult = await SecurityService.sendVerifyCodeByCustomer({ phone: phoneNumber });
    if (sendVerifyCodeResult.status === APIStatusCodeEnum.Success) {
      toastSuccess('驗證碼已傳送');
      phoneVerifyStatus.value = PhoneVerifyStatusEnum.VerifyCodeSent;
      startCountdown();
    } else {
      if (sendVerifyCodeResult.status === APIStatusCodeEnum.PhoneVerifySendExceeded) {
        toastError('驗證碼傳送次數過多');
        return;
      }
      toastError('驗證碼傳送失敗');
    }
  }
};

const startCountdown = () => {
  sentCodeCountDown.value = 60;
  countdownInterval.value = window.setInterval(() => {
    if (sentCodeCountDown.value !== null) {
      sentCodeCountDown.value -= 1;
      console.log(sentCodeCountDown.value);
      if (sentCodeCountDown.value <= 0) {
        clearInterval(countdownInterval.value!);
        sentCodeCountDown.value = null;
        phoneVerifyStatus.value = PhoneVerifyStatusEnum.Unverified;
        console.log('clear');
      }
    }
  }, 1000);
};

const checkVerifyCode = async (code: string) => {
  const checkResult = await SecurityService.checkVerifyCodeByCustomer({ verifyCode: code });
  if (checkResult.status === APIStatusCodeEnum.Success) {
    toastSuccess('驗證成功');
    phoneVerifyStatus.value = PhoneVerifyStatusEnum.Verified;
    clearCountdown();
    registerInput.value.phoneVerifyId = checkResult.phoneVerifyId;
  } else {
    toastError('驗證失敗');
  }
};

const register = async () => {
  if (phoneVerifyStatus.value !== PhoneVerifyStatusEnum.Verified) {
    toastError('請先完成手機號碼驗證');
    return;
  }

  if (!registerInput.value.agreeTerm) {
    toastError('請勾選同意服務條款');
    return;
  }

  const { password, rePassword } = registerInput.value;
  if (password !== rePassword) {
    toastError('密碼不一致');
    return;
  }

  if (!passwordValid(password)) {
    toastError('密碼格式錯誤');
    return;
  }

  const ciphertext = await hashPassword(password);
  const registerResult = await SecurityService.registerAndLoginByCustomer({
    phoneVerifyId: registerInput.value.phoneVerifyId,
    password: ciphertext
  });
  if (registerResult.status !== APIStatusCodeEnum.Success) {
    toastError('註冊失敗');
    return;
  }
  if (props.mode === 'full') {
    await submitCustomerMeasureData();
  }
  customerInfoStore.registerSuccess(registerResult.userId,phone.value,registerInput.value.password)
  toastSuccess('註冊成功');
  resetRegister();
  if (props.mode !== 'full') {
    await router.push({ name: 'customerorderlist' });
  }
};

const resetRegister = () => {
  phone.value = '';
  verifyCode.value = '';
  phoneVerifyStatus.value = PhoneVerifyStatusEnum.Unverified;
  registerInput.value = {
    phoneVerifyId: '',
    password: '',
    rePassword: '',
    agreeTerm: false
  };
};

const clearCountdown = () => {
  if (countdownInterval.value !== null) {
    clearInterval(countdownInterval.value);
    countdownInterval.value = null;
  }
};

//---------------------------------------------------登入區塊


const proccesLogin = async () => {
  if (!loginInput.value.phone || !loginInput.value.password) {
    toastError('請輸入完整登入資訊');
    return;
  }
  if (!(phoneNumberValid(loginInput.value.phone)) || !(passwordValid(loginInput.value.password))) {
    toastError('請輸入正確的登入資訊格式');
    return;
  }
  const loginSuccess = await customerInfoStore.login(loginInput.value);
  if (loginSuccess) {
    // 根據模式決定登入成功後的行為
    if (props.mode === 'full') {
      // 完整版：重置登入輸入但不清空，並檢查重複地址邏輯
      loginInput.value = {
        phone: '',
        password: '',
        rememberForShow: true
      };
      // 已登入狀態去檢查是否有重複地址
      const response = await CustomerMeasureOrderDetailService.getManyOrderDetail({});
      if (response.status === APIStatusCodeEnum.Success) {
        const found = response.result.some((item) => {
          if (item.address === step1Data.measureData.address.name) {
            router.push({ name: 'customerduplicateaddress' });
            return true;
          }
          return false;
        });
        if (!found) {
          await submitCustomerMeasureData();
        }
      } else {
        console.error('error');
      }
    } else {
      // 簡化版：重置登入輸入並直接重定向
      resetLoginInput();
      await router.push({ name: props.redirectRoute });
    }
  }
};

const resetLoginInput = () => {
  loginInput.value = {
    phone: '',
    password: '',
    rememberForShow: true
  };
};

const checkRememberPassword = () => {
  // 判斷地端暫存的登入資訊是否存在，如果存在則將資訊填入

  if (customerInfoStore.rememberMe) {
    loginInput.value = {
      phone: decryptAES(customerInfoStore.loginCipher.phone),
      password: decryptAES(customerInfoStore.loginCipher.password),
      rememberForShow: true
    };
  }
};

const submitCustomerMeasureData = async () => {
  // Call 刊登 API
  if (!step1Data.measureData) return;
  const response = await CustomerMeasureOrderService.publishOrder(step1Data.measureData);
  if (response.status === APIStatusCodeEnum.Success) {
    // 成功後立即綁定訂單
    const bindResponse = await CustomerMeasureOrderService.bindOrder({ orderIds: [response.result.orderId] });
    if (bindResponse.status === APIStatusCodeEnum.Success) {
      // 成功後跳轉訂單成立Modal 並清空pinia資料
      console.log('訂單成立');
      customerStep1PublishStore().cleanData();
      await router.push({
        name: 'customermeasurepublishok'
      });
    } else {
      console.error('error');
    }
  } else {
    console.error('error');
  }
};

onMounted(() => {
  if (customerInfoStore.loginState) router.push({ name: 'customerhome' });
  checkRememberPassword();
  if (customerInfoStore.hasEverLogin) {
    isNowDoingLogin.value = true;
  }
});

onBeforeUnmount(() => {
  resetRegister();
  resetLoginInput();
  clearCountdown();
});

</script>

<template>
  <div class="flex cus-border space-x-4 my-8 lg:h-[650px]">
    <!-- 左側區域 -->
    <div class="w-1/2 relative max-md:hidden">
      <img src="/image/login_beauty_pic.jpg" alt="" class="object-cover h-full w-full relative left-0 rounded-xl">
      <div class="absolute inset-0 flex items-center justify-center bg-gray-800 bg-opacity-50 rounded-xl">
      </div>
    </div>

    <!-- 右側區域 -->
    <!-- 註冊 -->
    <div v-if="!isNowDoingLogin"
         class="w-1/2 max-md:w-full flex flex-col justify-center items-center max-lg:items-start p-8">
      <!-- 完整版標題結構 -->
      <template v-if="mode === 'full'">
        <h2 class="text-lg font-bold mb-4">您的訂單已經成功送出</h2>
        <h2 class="text-lg font-bold mb-4">為了確保到府服務的真實性以及方便聯絡</h2>
        <p class="text-lg mb-4">請驗證您的手機號碼及設定密碼</p>
      </template>
      <!-- 簡化版標題結構 -->
      <template v-else>
        <h2 class="text-lg font-bold mb-4">為了確保到府服務的真實性以及方便聯絡</h2>
        <p class="text-lg mb-4">請驗證您的手機號碼及設定密碼</p>
      </template>
      <p class="text-black mb-4">已經註冊過家易帳號？
        <span class="text-blue-700 font-bold cursor-pointer" @click="isNowDoingLogin = true">立即登入！</span>
      </p>

      <div class="w-full max-w-md space-y-4">
        <label class="font-bold text-gray-700">驗證手機
          <span v-if="phoneVerifyStatus === PhoneVerifyStatusEnum.Verified" class="text-blue-600">
            驗證成功</span>
          <span v-else-if="phoneVerifyStatus === PhoneVerifyStatusEnum.VerifyCodeSent" class="text-red-500">
              驗證碼已傳送 <span v-if="sentCodeCountDown !== null">({{ sentCodeCountDown }})</span>
            </span>
          <span v-else class="text-red-500">未驗證</span></label>
        <div class="flex items-center gap-x-2 mt-2">
          <label for="phone" class="sr-only">Phone</label>
          <input id="phone" name="phone" type="tel" v-model="phone"
                 :disabled="phoneVerifyStatus === PhoneVerifyStatusEnum.Verified"
                 class="cus-border border border-gray-500 p-2 rounded-xl w-full focus-border-color hez-disable"
                 placeholder="請輸入手機號碼" />
          <button type="button"
                  :disabled="phoneVerifyStatus === PhoneVerifyStatusEnum.Verified || phoneVerifyStatus === PhoneVerifyStatusEnum.VerifyCodeSent"
                  class="cus-btn button-padding md:col-start-2 hez-disable text-nowrap md:text-nowrap lg:w-2/5"
                  @click="sendCode(phone)">發送驗證碼
          </button>
        </div>

        <div>
          <div class="flex items-center gap-x-2 mt-2">
            <label for="OTP" class="sr-only">Password</label>
            <input id="OTP" name="OTP" type="tel"
                   v-model="verifyCode" :disabled="phoneVerifyStatus === PhoneVerifyStatusEnum.Verified"
                   class="cus-border border border-gray-500 p-2 rounded-xl w-full focus-border-color hez-disable"
                   placeholder="請輸入驗證碼" />
            <button type="button" :disabled="phoneVerifyStatus === PhoneVerifyStatusEnum.Verified"
                    class="cus-btn button-padding md:col-start-2 hez-disable text-nowrap md:w-2/5"
                    @click="checkVerifyCode(verifyCode)">驗證
            </button>
          </div>
        </div>

        <div>
          <label class="font-bold text-gray-700">設定登入密碼</label>
          <p class="text-sm text-gray-500 mb-2">密碼至少為六個位元，需包含英文與數字</p>

          <div class="relative mb-2">
            <label for="password" class="sr-only">Password</label>
            <input id="password" name="password" :type="showPassword ? 'text' : 'password'"
                   v-model="registerInput.password"
                   :disabled="phoneVerifyStatus !== PhoneVerifyStatusEnum.Verified"
                   class="cus-border border border-gray-500 p-2 rounded-xl w-full focus-border-color"
                   placeholder="請輸入密碼" />
            <EyeIcon v-if="showPassword" @click="showPassword = false"
                     class="absolute right-2 top-2 h-6 w-6 text-black cursor-pointer" />
            <EyeSlashIcon v-else @click="showPassword = true"
                          class="absolute right-2 top-2 h-6 w-6 text-black cursor-pointer" />
          </div>
          <div class="relative">
            <label for="rePassword" class="sr-only">rePassword</label>
            <input id="rePassword" name="rePassword" :type="showRePassword ? 'text' : 'password'"
                   v-model="registerInput.rePassword"
                   :disabled="phoneVerifyStatus !== PhoneVerifyStatusEnum.Verified"
                   class="cus-border border border-gray-500 p-2 rounded-xl w-full focus-border-color"
                   placeholder="請再次輸入密碼" />
            <EyeIcon v-if="showRePassword" @click="showRePassword = false"
                     class="absolute right-2 top-2 h-6 w-6 text-black cursor-pointer" />
            <EyeSlashIcon v-else @click="showRePassword = true"
                          class="absolute right-2 top-2 h-6 w-6 text-black cursor-pointer" />
          </div>
        </div>

        <div class="flex gap-x-1 items-center mt-4">
          <input id="remember-me" name="remember-me" type="checkbox"
                 class="h-4 w-4 rounded border-gray-300 focus:ring-gray-600"
                 v-model="registerInput.agreeTerm" />
          <div class="flex justify-center gap-x-0.5 max-md:flex-wrap">
            <p>我已閱讀並同意</p>
            <a href="/customer/terms" target="_blank"
               class="font-bold text-red-500 cursor-pointer hover:underline">服務條款</a>
          </div>
        </div>

        <!-- 完整版：Footer 在註冊按鈕之前 -->
        <template v-if="mode === 'full'">
          <div class="mt-1 md:mt-2">
            <p>如果無法收到驗證碼或驗證相關問題</p>
            <div class="flex justify-start gap-x-0.5 max-md:flex-wrap">
              <p class="text-nowrap">請聯絡客服：</p>
              <a href="mailto:<EMAIL>"
                 class="font-bold text-red-500 cursor-pointer break-all hover:underline"><EMAIL></a>
            </div>
          </div>
          <button @click="register"
                  class="cus-btn w-full button-padding">
            註冊
          </button>
        </template>

        <!-- 簡化版：註冊按鈕在 Footer 之前 -->
        <template v-else>
          <button @click="register"
                  class="cus-btn w-full button-padding">
            註冊
          </button>
          <div class="mt-1 md:mt-2">
            <p>如果無法收到驗證碼或驗證相關問題</p>
            <div class="flex justify-start gap-x-0.5 max-md:flex-wrap">
              <p class="text-nowrap">請聯絡客服：</p>
              <a href="mailto:<EMAIL>"
                 class="font-bold text-red-500 cursor-pointer break-all hover:underline"><EMAIL></a>
            </div>
          </div>
        </template>
      </div>
    </div>

    <!-- 登入 -->
    <div v-else class="w-1/2 max-md:w-full flex flex-col justify-center items-center p-8">
      <h2 class="text-lg font-bold mb-4">登入</h2>
      <p class="text-black mb-4">尚未註冊過家易帳號？
        <span class="text-red-500 font-bold cursor-pointer" @click="isNowDoingLogin = false">立即註冊！</span>
      </p>
      <div class="w-full max-w-md space-y-2">
        <div class="relative flex flex-col space-y-2">
          <div>
            <label for="phone" class="sr-only">Phone</label>
            <input id="phone" name="phone" type="tel" v-model="loginInput.phone"
                   class="cus-border border border-gray-500 p-2 rounded-xl w-full focus-border-color"
                   placeholder="請輸入手機號碼" />
          </div>
          <div class="relative">
            <label for="password" class="sr-only">Password</label>
            <input id="password" name="password" :type="showLoginPassword ? 'text' : 'password'"
                   v-model="loginInput.password"
                   class="cus-border border border-gray-500 p-2 rounded-xl w-full focus-border-color"
                   placeholder="請輸入密碼" />
            <EyeIcon v-if="showLoginPassword" @click="showLoginPassword = false"
                     class="absolute right-2 top-2 h-6 w-6 text-black cursor-pointer" />
            <EyeSlashIcon v-else @click="showLoginPassword = true"
                          class="absolute right-2 top-2 h-6 w-6 text-black cursor-pointer" />
          </div>
        </div>

        <div class="flex gap-x-1 items-center mt-4">
          <div class="mt-1 md:mt-2 flex-center justify-start">
            <input id="remember-me" name="remember-me" type="checkbox"
                   class="h-4 w-4 rounded border-gray-300 focus:ring-gray-600" v-model="loginInput.rememberForShow" />
            <label for="remember-me" class="ml-2 block leading-6">記住密碼</label>
          </div>
        </div>
        <button @click="proccesLogin"
                class="cus-btn w-full button-padding">
          登入
        </button>
        <div class="mt-1 md:mt-2">
          <p>如果忘記密碼，可用<span
            class="text-red-500 font-bold cursor-pointer" @click="isNowDoingLogin = false">手機驗證</span></p>
          <div class="flex justify-start gap-x-0.5 max-md:flex-wrap">
            <p class="text-nowrap">請聯絡客服：</p>
            <a href="mailto:<EMAIL>"
               class="font-bold text-red-500 cursor-pointer break-all hover:underline"><EMAIL></a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
