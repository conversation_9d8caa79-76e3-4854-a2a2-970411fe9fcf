<script setup lang="ts">
import DefaultModal from '@/components/Order/OrderDetail/UpdateModal/DefaultModal.vue';
import { ref } from 'vue';
import { UserTypeEnum } from '@/model/enum/userTypeEnum.ts';

defineProps<{
  userType: UserTypeEnum;
  phoneNumber: string;
}>();
const ModalRef = ref<InstanceType<typeof DefaultModal> | null>(null);

const openModal = () => {
  ModalRef.value?.openModal();
};

defineExpose({ openModal });
</script>

<template>
  <DefaultModal
    title="手機電話"
    :show-close-button="true"
    :click-outside-close="true"
    modalWidth="max-w-xl"
    ref="ModalRef"
    @closeModal="ModalRef?.closeModal()"
  >
    <div class="flex flex-col items-center text-black">
      <div class="flex flex-col items-center space-y-3 p-4 text-center">
        <!-- 根據用戶類型顯示不同的提示文字 -->
        <p class="text-lg font-bold" v-if="userType === UserTypeEnum.Customer">設計師電話號碼</p>
        <p class="text-lg font-bold" v-else-if="userType === UserTypeEnum.Designer">客戶電話號碼</p>

        <!-- 顯示電話號碼 -->
        <p class="text-2xl font-bold tracking-wider text-blue-600">
          {{ phoneNumber }}
        </p>
      </div>
    </div>
  </DefaultModal>
</template>
